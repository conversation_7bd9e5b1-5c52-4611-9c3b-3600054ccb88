#!/usr/bin/env python3
"""
Test script to verify the modular HTTP structure works with Werkzeug 3.0.1
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🧪 Testing modular HTTP structure...")
    
    # Test all modular imports
    from odoo.http import Application, Request, Response
    print("✅ Core HTTP classes imported")
    
    from odoo.http.wsgi import Application as WSGIApp
    print("✅ WSGI module imported")
    
    from odoo.http.request import Request as HTTPRequest
    print("✅ Request module imported")
    
    from odoo.http.response import Response as HTTPResponse
    print("✅ Response module imported")
    
    from odoo.http.session import Session
    print("✅ Session module imported")
    
    from odoo.http.utils import ROUTING_KEYS
    print("✅ Utils module imported")
    
    from odoo.http.routing import route
    print("✅ Routing module imported")
    
    from odoo.http.dispatchers import HttpDispatcher
    print("✅ Dispatchers module imported")
    
    # Test Werkzeug compatibility
    import werkzeug
    from werkzeug.wrappers import Request as WerkzeugRequest, Response as WerkzeugResponse
    from werkzeug.serving import run_simple
    print(f"✅ Werkzeug {werkzeug.__version__} compatibility verified")
    
    # Create a simple test application
    def test_app(environ, start_response):
        request = WerkzeugRequest(environ)
        if request.path == '/':
            content = """
            <html>
            <head><title>Odoo HTTP Test</title></head>
            <body>
                <h1>✅ Odoo Modular HTTP Structure Test</h1>
                <p>Werkzeug version: {}</p>
                <p>All modular HTTP components imported successfully!</p>
                <p>Root path "/" is accessible without errors.</p>
            </body>
            </html>
            """.format(werkzeug.__version__)
            response = WerkzeugResponse(content, content_type='text/html')
        else:
            response = WerkzeugResponse('Not Found', status=404)
        return response(environ, start_response)
    
    print("\n🎯 All modular HTTP imports successful!")
    print("🚀 Starting test server on http://localhost:8071")
    print("📝 Access http://localhost:8071/ to verify root path works")
    print("📝 Press Ctrl+C to stop the server")
    
    # Start the test server
    run_simple('localhost', 8071, test_app, use_reloader=False, use_debugger=True)
    
except ImportError as e:
    print(f"❌ Import error in modular structure: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
