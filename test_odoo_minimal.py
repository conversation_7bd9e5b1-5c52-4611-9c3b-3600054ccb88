#!/usr/bin/env python3
"""
Minimal Odoo server test to verify HTTP functionality
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up basic logging
logging.basicConfig(level=logging.INFO)

try:
    print("🚀 Starting minimal Odoo HTTP test...")
    
    # Import Odoo
    import odoo
    from odoo.tools import config
    
    # Set minimal configuration
    config.parse_config([
        '--http-interface=0.0.0.0',
        '--http-port=8069',
        '--no-database-list',
        '--log-level=info',
        '--addons-path=./addons'
    ])
    
    print("✅ Configuration parsed successfully")
    
    # Import HTTP components
    from odoo.http import Application
    from werkzeug.serving import run_simple
    
    print("✅ HTTP components imported successfully")
    
    # Create the WSGI application
    application = Application()
    print("✅ Odoo Application created successfully")
    
    print("🌐 Starting Odoo HTTP server on http://localhost:8069")
    print("📝 Press Ctrl+C to stop the server")
    
    # Start the server
    run_simple('0.0.0.0', 8069, application, use_reloader=False, use_debugger=True)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
