#!/usr/bin/env python3
"""
Simple test script to verify Odoo HTTP functionality with Werkzeug 3.0.1
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Test imports
    print("Testing imports...")
    import odoo
    print("✅ odoo imported successfully")
    
    import odoo.http
    print("✅ odoo.http imported successfully")
    
    from odoo.http import Application, Request, Response
    print("✅ HTTP classes imported successfully")
    
    # Test Werkzeug version
    import werkzeug
    print(f"✅ Werkzeug version: {werkzeug.__version__}")
    
    # Test basic HTTP functionality
    print("\nTesting HTTP functionality...")
    
    # Create a simple WSGI application
    from werkzeug.wrappers import Request as WerkzeugRequest, Response as WerkzeugResponse
    
    def simple_app(environ, start_response):
        request = WerkzeugRequest(environ)
        if request.path == '/':
            response = WerkzeugResponse('Hello from Odoo HTTP test!', content_type='text/plain')
        else:
            response = WerkzeugResponse('Not Found', status=404)
        return response(environ, start_response)
    
    # Test if we can create the application
    from werkzeug.serving import run_simple
    print("✅ Werkzeug serving imported successfully")
    
    print("\n🎯 All HTTP imports and basic functionality tests passed!")
    print("🚀 Starting test server on http://localhost:8070")
    print("📝 Press Ctrl+C to stop the server")
    
    # Start the test server
    run_simple('localhost', 8070, simple_app, use_reloader=False, use_debugger=True)
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
